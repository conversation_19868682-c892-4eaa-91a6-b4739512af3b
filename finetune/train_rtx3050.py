#!/usr/bin/env python3
"""
RTX 3050 Optimized Piper Training Script
Based on official TRAINING.md guide
"""

import os
import subprocess
import sys
from pathlib import Path
import json

def run_command(cmd, cwd=None, check=True):
    """Run shell command and return result"""
    print(f"Running: {cmd}")
    if cwd:
        print(f"Working directory: {cwd}")
    
    result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
    
    if result.stdout:
        print("STDOUT:", result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    
    if check and result.returncode != 0:
        print(f"Error: Command failed with return code {result.returncode}")
        sys.exit(1)
    
    return result

def setup_environment():
    """Set up the training environment"""
    print("🔧 Setting up training environment...")
    
    # Paths
    base_dir = Path("/home/<USER>/Documents/personal/test")
    piper_dir = base_dir / "finetune" / "piper"
    python_dir = piper_dir / "src" / "python"
    
    print(f"Base directory: {base_dir}")
    print(f"Piper directory: {piper_dir}")
    print(f"Python directory: {python_dir}")
    
    # Check if piper exists
    if not piper_dir.exists():
        print("❌ Piper repository not found!")
        print("Please run: git clone https://github.com/rmcpantoja/piper.git finetune/piper")
        sys.exit(1)
    
    # Change to python directory
    os.chdir(python_dir)
    print(f"Changed to: {os.getcwd()}")
    
    # Check virtual environment
    venv_path = base_dir / ".venv"
    if not venv_path.exists():
        print("❌ Virtual environment not found!")
        print("Please create virtual environment first")
        sys.exit(1)
    
    # Activate virtual environment
    activate_script = venv_path / "bin" / "activate"
    if activate_script.exists():
        print("✅ Virtual environment found")
    
    return base_dir, piper_dir, python_dir

def install_dependencies(python_dir):
    """Install Piper training dependencies"""
    print("📦 Installing Piper training dependencies...")
    
    # Install piper training package
    run_command("pip install -e .", cwd=python_dir)
    
    # Build monotonic align
    build_script = python_dir / "build_monotonic_align.sh"
    if build_script.exists():
        print("🔨 Building monotonic align...")
        run_command("bash build_monotonic_align.sh", cwd=python_dir)
    else:
        print("⚠️ build_monotonic_align.sh not found")
    
    # Check espeak-ng
    result = run_command("which espeak-ng", check=False)
    if result.returncode != 0:
        print("⚠️ espeak-ng not found. Installing...")
        run_command("sudo apt-get update && sudo apt-get install -y espeak-ng")

def preprocess_dataset(base_dir, python_dir):
    """Preprocess the Nepali dataset"""
    print("🔄 Preprocessing Nepali dataset...")
    
    dataset_dir = base_dir / "dataset"
    output_dir = base_dir / "finetune" / "output"
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Check if dataset exists
    metadata_file = dataset_dir / "metadata.csv"
    if not metadata_file.exists():
        print("❌ Dataset not prepared!")
        print("Please run: python finetune/prepare_dataset.py")
        sys.exit(1)
    
    # Preprocess command
    preprocess_cmd = f"""python -m piper_train.preprocess \\
        --language ne \\
        --input-dir {dataset_dir} \\
        --output-dir {output_dir} \\
        --dataset-format ljspeech \\
        --single-speaker \\
        --sample-rate 22050"""
    
    run_command(preprocess_cmd, cwd=python_dir)
    
    # Check if preprocessing was successful
    config_file = output_dir / "config.json"
    dataset_file = output_dir / "dataset.jsonl"
    
    if config_file.exists() and dataset_file.exists():
        print("✅ Preprocessing completed successfully")
        
        # Show dataset info
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        with open(dataset_file, 'r') as f:
            dataset_lines = f.readlines()
        
        print(f"📊 Dataset info:")
        print(f"  - Speakers: {config.get('num_speakers', 'unknown')}")
        print(f"  - Sample rate: {config.get('audio', {}).get('sample_rate', 'unknown')} Hz")
        print(f"  - Utterances: {len(dataset_lines)}")
        
        return output_dir
    else:
        print("❌ Preprocessing failed!")
        sys.exit(1)

def train_model(python_dir, output_dir):
    """Train the model with RTX 3050 optimized settings"""
    print("🚀 Starting training with RTX 3050 optimized settings...")
    
    # RTX 3050 optimized parameters
    batch_size = 2  # Conservative for 6GB VRAM
    max_epochs = 50  # Reduced for testing
    precision = "16-mixed"  # Memory efficient
    
    print(f"⚙️ Training parameters:")
    print(f"  - Batch size: {batch_size}")
    print(f"  - Max epochs: {max_epochs}")
    print(f"  - Precision: {precision}")
    print(f"  - Quality: medium")
    
    # Training command
    train_cmd = f"""python -m piper_train \\
        --dataset-dir {output_dir} \\
        --accelerator gpu \\
        --devices 1 \\
        --batch-size {batch_size} \\
        --validation-split 0.01 \\
        --num-test-examples 2 \\
        --max_epochs {max_epochs} \\
        --checkpoint-epochs 5 \\
        --precision {precision} \\
        --quality medium"""
    
    print("🎯 Starting training...")
    print("This will take approximately 2-3 hours for 50 epochs")
    print("Monitor GPU usage with: watch -n 1 nvidia-smi")
    
    run_command(train_cmd, cwd=python_dir)

def main():
    """Main training function"""
    print("🎮 RTX 3050 Piper Training Script")
    print("=" * 50)
    
    # Setup
    base_dir, piper_dir, python_dir = setup_environment()
    
    # Install dependencies
    install_dependencies(python_dir)
    
    # Preprocess dataset
    output_dir = preprocess_dataset(base_dir, python_dir)
    
    # Train model
    train_model(python_dir, output_dir)
    
    print("🎉 Training completed!")
    print(f"Check results in: {output_dir}/lightning_logs/")
    print("To export model: python -m piper_train.export_onnx <checkpoint> <output.onnx>")

if __name__ == "__main__":
    main()

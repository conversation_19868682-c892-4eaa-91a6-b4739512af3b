# ## <font color="pink"> **Google Colab Anti-Disconnect.** 🔌
# ---
# #### Avoid automatic disconnection. Still, it will disconnect after <font color="orange">**6 to 12 hours**</font>.
# COMMENTED OUT - NOT NEEDED FOR LOCAL TRAINING

# import IPython
# js_code = '''
# function ClickConnect(){
# console.log("Working");
# document.querySelector("colab-toolbar-button#connect").click()
# }
# setInterval(ClickConnect,60000)
# '''
# display(IPython.display.Javascript(js_code))

print("Running on local RTX 3050 6GB - No anti-disconnect needed!")

## <font color="pink"> **Check GPU type.** 👁️
---
#### RTX 3050 6GB Laptop GPU - Optimized settings for local training

import subprocess
import json

# Check GPU details
result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total,memory.free,memory.used,temperature.gpu', '--format=csv,noheader,nounits'], 
                       capture_output=True, text=True)
gpu_info = result.stdout.strip().split(', ')

print(f"🎮 GPU: {gpu_info[0]}")
print(f"💾 Total VRAM: {gpu_info[1]} MB")
print(f"🆓 Free VRAM: {gpu_info[2]} MB")
print(f"📊 Used VRAM: {gpu_info[3]} MB")
print(f"🌡️ Temperature: {gpu_info[4]}°C")

# RTX 3050 optimized settings
print("\n⚙️ RTX 3050 Optimized Settings:")
print("- Batch Size: 2-4 (conservative for 6GB)")
print("- Precision: 16-mixed (memory efficient)")
print("- Quality: medium (good balance)")
print("- Expected training time: 2-3 hours for 25 epochs")

# <font color="pink"> **Mount Google Drive.** 📂
# COMMENTED OUT - Using local filesystem instead

# from google.colab import drive
# drive.mount('/content/drive', force_remount=True)

import os
from pathlib import Path

# Set up local paths
BASE_DIR = Path('/home/<USER>/Documents/personal/test')
DATASET_DIR = BASE_DIR / 'dataset'
OUTPUT_DIR = BASE_DIR / 'finetune' / 'output'
MODEL_DIR = BASE_DIR / 'finetune' / 'models'

# Create directories
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
MODEL_DIR.mkdir(parents=True, exist_ok=True)

print(f"📁 Base directory: {BASE_DIR}")
print(f"📁 Dataset directory: {DATASET_DIR}")
print(f"📁 Output directory: {OUTPUT_DIR}")
print(f"📁 Model directory: {MODEL_DIR}")
print("\n✅ Local filesystem ready!")

# <font color="pink"> **Install software.** 📦

# Local installation for RTX 3050 - Using existing environment

import subprocess
import os
from pathlib import Path

# Check if we're in the right environment
print("🔍 Checking environment...")
print(f"Python: {subprocess.check_output(['python', '--version']).decode().strip()}")

# Check if piper repository exists
piper_dir = BASE_DIR / 'piper'
if not piper_dir.exists():
    print("📥 Cloning Piper repository...")
    subprocess.run(['git', 'clone', 'https://github.com/rmcpantoja/piper.git'], cwd=BASE_DIR)
else:
    print("✅ Piper repository already exists")

# Change to piper directory
piper_python_dir = piper_dir / 'src' / 'python'
os.chdir(piper_python_dir)

# Download resample script if needed
resample_file = piper_python_dir / 'resample.py'
if not resample_file.exists():
    print("📥 Downloading resample script...")
    subprocess.run(['wget', '-q', 'https://raw.githubusercontent.com/coqui-ai/TTS/dev/TTS/bin/resample.py'])

# Build monotonic align if needed
if not (piper_python_dir / 'monotonic_align').exists():
    print("🔨 Building monotonic align...")
    subprocess.run(['bash', 'build_monotonic_align.sh'])
else:
    print("✅ Monotonic align already built")

# Check PyTorch installation
try:
    import torch
    print(f"✅ PyTorch {torch.__version__} installed")
    print(f"✅ CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
except ImportError:
    print("❌ PyTorch not installed - please wait for installation to complete")

print("\n🎯 RTX 3050 Setup Complete!")
print("Ready for training with optimized settings.")

# <font color="pink"> **1. Extract dataset.** 📥
# Using OpenSLR 43 Nepali dataset - already downloaded

import zipfile
import os
from pathlib import Path

# Check if dataset is downloaded
dataset_zip = DATASET_DIR / 'ne_np_female.zip'
wavs_dir = DATASET_DIR / 'wavs'

print(f"📁 Dataset directory: {DATASET_DIR}")
print(f"📦 Dataset zip: {dataset_zip}")

if dataset_zip.exists():
    print("✅ Dataset zip found")
    
    # Create wavs directory
    wavs_dir.mkdir(exist_ok=True)
    
    # Check if already extracted
    if not any(DATASET_DIR.glob('**/*.wav')):
        print("📥 Extracting dataset...")
        with zipfile.ZipFile(dataset_zip, 'r') as zip_ref:
            zip_ref.extractall(DATASET_DIR)
        print("✅ Dataset extracted")
    else:
        print("✅ Dataset already extracted")
    
    # Count audio files
    audio_files = list(DATASET_DIR.glob('**/*.wav'))
    print(f"🎵 Found {len(audio_files)} audio files")
    
else:
    print("❌ Dataset not found. Please run the download script first.")
    print("Run: python finetune/simple_setup.py")

# <font color="pink"> **2. Prepare transcript file.** 📝
# Convert OpenSLR TSV to Piper CSV format

import csv
import librosa
import soundfile as sf
from pathlib import Path

# Run the dataset preparation script
print("🔄 Preparing Nepali dataset for training...")
print("This will:")
print("- Convert TSV transcripts to CSV format")
print("- Resample audio to 22050Hz")
print("- Create numbered WAV files")
print("- Limit to 300 samples for RTX 3050 testing")

# Change to base directory
os.chdir(BASE_DIR)

# Run preparation script
try:
    exec(open('finetune/prepare_dataset.py').read())
    print("✅ Dataset preparation completed")
except Exception as e:
    print(f"❌ Error preparing dataset: {e}")
    print("Please run manually: python finetune/prepare_dataset.py")

# Check if metadata.csv exists
metadata_file = DATASET_DIR / 'metadata.csv'
if metadata_file.exists():
    with open(metadata_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    print(f"✅ Metadata file created with {len(lines)} entries")
    print("Sample entries:")
    for i, line in enumerate(lines[:3]):
        print(f"  {line.strip()}")
else:
    print("❌ Metadata file not created. Please check dataset preparation.")

#@markdown # <font color="pink"> **3. Preprocess dataset.** 🔄

import os
#@markdown ### First of all, select the language of your dataset.
language = "English (U.S.)" #@param ["Català", "Dansk", "Deutsch", "Ελληνικά", "English (British)", "English (U.S.)", "Español", "Español (latinoamericano)", "Suomi", "Français", "Magyar", "Icelandic", "Italiano", "ქართული", "қазақша", "Lëtzebuergesch", "नेपाली", "Nederlands", "Norsk", "Polski", "Português (Brasil)", "Română", "Русский", "Српски", "Svenska", "Kiswahili", "Türkçe", "украї́нська", "Tiếng Việt", "简体中文"]
#@markdown ---
# language definition:
languages = {
    "Català": "ca",
    "Dansk": "da",
    "Deutsch": "de",
    "Ελληνικά": "grc",
    "English (British)": "en",
    "English (U.S.)": "en-us",
    "Español": "es",
    "Español (latinoamericano)": "es-419",
    "Suomi": "fi",
    "Français": "fr",
    "Magyar": "hu",
    "Icelandic": "is",
    "Italiano": "it",
    "ქართული": "ka",
    "қазақша": "kk",
    "Lëtzebuergesch": "lb",
    "नेपाली": "ne",
    "Nederlands": "nl",
    "Norsk": "nb",
    "Polski": "pl",
    "Português (Brasil)": "pt-br",
    "Română": "ro",
    "Русский": "ru",
    "Српски": "sr",
    "Svenska": "sv",
    "Kiswahili": "sw",
    "Türkçe": "tr",
    "украї́нська": "uk",
    "Tiếng Việt": "vi",
    "简体中文": "zh"
}

def _get_language(code):
    return languages[code]

final_language = _get_language(language)
#@markdown ### Choose a name for your model:
model_name = "Test" #@param {type:"string"}
#@markdown ---
# output:
#@markdown ### Choose the working folder: (recommended to save to Drive)

#@markdown The working folder will be used in preprocessing, but also in training the model.
output_path = "/content/drive/MyDrive/colab/piper" #@param {type:"string"}
output_dir = output_path+"/"+model_name
if not os.path.exists(output_dir):
  os.makedirs(output_dir)
#@markdown ---
#@markdown ### Choose dataset format:
dataset_format = "ljspeech" #@param ["ljspeech", "mycroft"]
#@markdown ---
#@markdown ### Is this a single speaker dataset? Otherwise, uncheck:
single_speaker = True #@param {type:"boolean"}
if single_speaker:
  force_sp = " --single-speaker"
else:
  force_sp = ""
#@markdown ---
#@markdown ### Select the sample rate of the dataset:
sample_rate = "22050" #@param ["16000", "22050"]
#@markdown ---
%cd /content/piper/src/python
#@markdown ### Do you want to train using this sample rate, but your audios don't have it?
#@markdown The resampler helps you do it quickly!
resample = False #@param {type:"boolean"}
if resample:
  !python resample.py --input_dir "/content/dataset/wavs" --output_dir "/content/dataset/wavs_resampled" --output_sr {sample_rate} --file_ext "wav"
  !mv /content/dataset/wavs_resampled/* /content/dataset/wavs
#@markdown ---

!python -m piper_train.preprocess \
  --language {final_language} \
  --input-dir /content/dataset \
  --output-dir "{output_dir}" \
  --dataset-format {dataset_format} \
  --sample-rate {sample_rate} \
  {force_sp}

#@markdown # <font color="pink"> **4. Settings.** 🧰
import json
import ipywidgets as widgets
from IPython.display import display
from google.colab import output
import os
#@markdown ### Select the action to train this dataset:

#@markdown * The option to continue a training is self-explanatory. If you've previously trained a model with free colab, your time is up and you're considering training it some more, this is ideal for you. You just have to set the same settings that you set when you first trained this model.
#@markdown * The option to convert a single-speaker model to a multi-speaker model is self-explanatory, and for this it is important that you have processed a dataset that contains text and audio from all possible speakers that you want to train in your model.
#@markdown * The finetune option is used to train a dataset using a pretrained model, that is, train on that data. This option is ideal if you want to train a very small dataset (more than five minutes recommended).
#@markdown * The train from scratch option builds features such as dictionary and speech form from scratch, and this may take longer to converge. For this, hours of audio (8 at least) are recommended, which have a large collection of phonemes.

action = "finetune" #@param ["Continue training", "convert single-speaker to multi-speaker model", "finetune", "train from scratch"]
#@markdown ---
if action == "Continue training":
    if os.path.exists(f"{output_dir}/lightning_logs/version_0/checkpoints/last.ckpt"):
        ft_command = f'--resume_from_checkpoint "{output_dir}/lightning_logs/version_0/checkpoints/last.ckpt" '
        print(f"Continuing {model_name}'s training at: {output_dir}/lightning_logs/version_0/checkpoints/last.ckpt")
    else:
        raise Exception("Training cannot be continued as there is no checkpoint to continue at.")
elif action == "finetune":
    if os.path.exists(f"{output_dir}/lightning_logs/version_0/checkpoints/last.ckpt"):
        raise Exception("Oh no! You have already trained this model before, you cannot choose this option since your progress will be lost, and then your previous time will not count. Please select the option to continue a training.")
    else:
        ft_command = '--resume_from_checkpoint "/content/pretrained.ckpt" '
elif action == "convert single-speaker to multi-speaker model":
    if not single_speaker:
        ft_command = '--resume_from_single_speaker_checkpoint "/content/pretrained.ckpt" '
    else:
        raise Exception("This dataset is not a multi-speaker dataset!")
else:
    ft_command = ""
if action== "convert single-speaker to multi-speaker model" or action == "finetune":
    try:
        with open('/content/piper/notebooks/pretrained_models.json') as f:
            pretrained_models = json.load(f)
        if final_language in pretrained_models:
            models = pretrained_models[final_language]
            model_options = [(model_name, model_name) for model_name, model_url in models.items()]
            model_dropdown = widgets.Dropdown(description = "Choose pretrained model", options=model_options)
            download_button = widgets.Button(description="Download")
            def download_model(btn):
                model_name = model_dropdown.value
                model_url = pretrained_models[final_language][model_name]
                print("Downloading pretrained model...")
                if model_url.startswith("1"):
                    !gdown -q "{model_url}" -O "/content/pretrained.ckpt"
                elif model_url.startswith("https://drive.google.com/file/d/"):
                    !gdown -q "{model_url}" -O "/content/pretrained.ckpt" --fuzzy
                else:
                    !wget -q "{model_url}" -O "/content/pretrained.ckpt"
                model_dropdown.close()
                download_button.close()
                output.clear()
                if os.path.exists("/content/pretrained.ckpt"):
                    print("Model downloaded!")
                else:
                    raise Exception("Couldn't download the pretrained model!")
            download_button.on_click(download_model)
            display(model_dropdown, download_button)
        else:
            raise Exception(f"There are no pretrained models available for the language {final_language}")
    except FileNotFoundError:
        raise Exception("The pretrained_models.json file was not found.")
else:
    print("Warning: this model will be trained from scratch. You need at least 8 hours of data for everything to work decent. Good luck!")
#@markdown ### Choose batch size based on this dataset:
batch_size = 12 #@param {type:"integer"}
#@markdown ---
validation_split = 0.01
#@markdown ### Choose the quality for this model:

#@markdown * x-low - 16Khz audio, 5-7M params
#@markdown * medium - 22.05Khz audio, 15-20 params
#@markdown * high - 22.05Khz audio, 28-32M params
quality = "medium" #@param ["high", "x-low", "medium"]
#@markdown ---
#@markdown ### For how many epochs to save training checkpoints?
#@markdown The larger your dataset, you should set this saving interval to a smaller value, as epochs can progress longer time.
checkpoint_epochs = 5 #@param {type:"integer"}
#@markdown ---
#@markdown ### Step interval to generate model samples:
log_every_n_steps = 1000 #@param {type:"integer"}
#@markdown ---
#@markdown ### Training epochs:
max_epochs = 10000 #@param {type:"integer"}
#@markdown ---

#@markdown # <font color="pink"> **5. Train.** 🏋️‍♂️
#@markdown Run this cell to train your final model! If possible, some audio samples will be saved during training in the output folder.

get_ipython().system(f'''
python -m piper_train \
--dataset-dir "{output_dir}" \
--accelerator 'gpu' \
--devices 1 \
--batch-size {batch_size} \
--validation-split {validation_split} \
--num-test-examples 2 \
--quality {quality} \
--checkpoint-epochs {checkpoint_epochs} \
--log_every_n_steps {log_every_n_steps} \
--max_epochs {max_epochs} \
{ft_command}\
--precision 32
''')
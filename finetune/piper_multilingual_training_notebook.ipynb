#@markdown ## <font color="pink"> **Google Colab Anti-Disconnect.** 🔌
#@markdown ---
#@markdown #### Avoid automatic disconnection. Still, it will disconnect after <font color="orange">**6 to 12 hours**</font>.

import IPython
js_code = '''
function ClickConnect(){
console.log("Working");
document.querySelector("colab-toolbar-button#connect").click()
}
setInterval(ClickConnect,60000)
'''
display(IPython.display.Javascript(js_code))

#@markdown ## <font color="pink"> **Check GPU type.** 👁️
#@markdown ---
#@markdown #### A higher capable GPU can lead to faster training speeds. By default, you will have a <font color="orange">**Tesla T4**</font>.
!nvidia-smi

#@markdown # <font color="pink"> **Mount Google Drive.** 📂
from google.colab import drive
drive.mount('/content/drive', force_remount=True)

#@markdown # <font color="pink"> **Install software.** 📦

#@markdown ####In this cell the synthesizer and its necessary dependencies to execute the training will be installed. (this may take a while)

#@markdown #### <font color="orange">**Do you want to use the patch?**
#@markdown The patch provides the ability to export audio files to the output folder and save a single model while training.
usepatch = True #@param {type:"boolean"}
#@markdown ---
# clone:
!git clone -q https://github.com/rmcpantoja/piper
%cd /content/piper/src/python
!wget -q "https://raw.githubusercontent.com/coqui-ai/TTS/dev/TTS/bin/resample.py"
#!pip install -q -r requirements.txt
!pip install -q cython>=0.29.0 piper-phonemize==1.1.0 librosa>=0.9.2 numpy>=1.19.0 onnxruntime>=1.11.0 pytorch-lightning==1.7.0 torch==1.11.0
!pip install -q torchtext==0.12.0 torchvision==0.12.0
# fixing recent compativility isswes:
!pip install -q torchaudio==0.11.0 torchmetrics==0.11.4
!bash build_monotonic_align.sh
!apt-get install -q espeak-ng
# download patches:
if usepatch:
    print("Downloading patch...")
    !gdown -q "1EWEb7amo1rgFGpBFfRD4BKX3pkjVK1I-" -O "/content/piper/src/python/patch.zip"
    !unzip -o -q "patch.zip"
%cd /content

#@markdown # <font color="pink"> **1. Extract dataset.** 📥
#@markdown ####Important: the audios must be in <font color="orange">**wav format, (16000 or 22050hz, 16-bits, mono), and, for convenience, numbered. Example:**

#@markdown * <font color="orange">**1.wav**</font>
#@markdown * <font color="orange">**2.wav**</font>
#@markdown * <font color="orange">**3.wav**</font>
#@markdown * <font color="orange">**.....**</font>

#@markdown ---

%cd /content
!mkdir /content/dataset
%cd /content/dataset
!mkdir /content/dataset/wavs
#@markdown ### Audio dataset path to unzip:
zip_path = "/content/drive/MyDrive/Wavs.zip" #@param {type:"string"}
!unzip "{zip_path}" -d /content/dataset/wavs
#@markdown ---

#@markdown # <font color="pink"> **2. Upload the transcript file.** 📝
#@markdown ---
#@markdown ####<font color="orange">**Important: the transcription means writing what the character says in each of the audios, and it must have the following structure:**

#@markdown ##### <font color="orange">For a single-speaker dataset:
#@markdown * wavs/1.wav|This is what my character says in audio 1.
#@markdown * wavs/2.wav|This, the text that the character says in audio 2.
#@markdown * ...

#@markdown ##### <font color="orange">For a multi-speaker dataset:

#@markdown * wavs/speaker1audio1.wav|speaker1|This is what the first speaker says.
#@markdown * wavs/speaker1audio2.wav|speaker1|This is another audio of the first speaker.
#@markdown * wavs/speaker2audio1.wav|speaker2|This is what the second speaker says in the first audio.
#@markdown * wavs/speaker2audio2.wav|speaker2|This is another audio of the second speaker.
#@markdown * ...

#@markdown And so on. In addition, the transcript must be in a <font color="orange">**.csv format. (UTF-8 without BOM)**

#@markdown ---
%cd /content/dataset
from google.colab import files
!rm /content/dataset/metadata.csv
listfn, length = files.upload().popitem()
if listfn != "metadata.csv":
  !mv "$listfn" metadata.csv
%cd ..

#@markdown # <font color="pink"> **3. Preprocess dataset.** 🔄

import os
#@markdown ### First of all, select the language of your dataset.
language = "English (U.S.)" #@param ["Català", "Dansk", "Deutsch", "Ελληνικά", "English (British)", "English (U.S.)", "Español", "Español (latinoamericano)", "Suomi", "Français", "Magyar", "Icelandic", "Italiano", "ქართული", "қазақша", "Lëtzebuergesch", "नेपाली", "Nederlands", "Norsk", "Polski", "Português (Brasil)", "Română", "Русский", "Српски", "Svenska", "Kiswahili", "Türkçe", "украї́нська", "Tiếng Việt", "简体中文"]
#@markdown ---
# language definition:
languages = {
    "Català": "ca",
    "Dansk": "da",
    "Deutsch": "de",
    "Ελληνικά": "grc",
    "English (British)": "en",
    "English (U.S.)": "en-us",
    "Español": "es",
    "Español (latinoamericano)": "es-419",
    "Suomi": "fi",
    "Français": "fr",
    "Magyar": "hu",
    "Icelandic": "is",
    "Italiano": "it",
    "ქართული": "ka",
    "қазақша": "kk",
    "Lëtzebuergesch": "lb",
    "नेपाली": "ne",
    "Nederlands": "nl",
    "Norsk": "nb",
    "Polski": "pl",
    "Português (Brasil)": "pt-br",
    "Română": "ro",
    "Русский": "ru",
    "Српски": "sr",
    "Svenska": "sv",
    "Kiswahili": "sw",
    "Türkçe": "tr",
    "украї́нська": "uk",
    "Tiếng Việt": "vi",
    "简体中文": "zh"
}

def _get_language(code):
    return languages[code]

final_language = _get_language(language)
#@markdown ### Choose a name for your model:
model_name = "Test" #@param {type:"string"}
#@markdown ---
# output:
#@markdown ### Choose the working folder: (recommended to save to Drive)

#@markdown The working folder will be used in preprocessing, but also in training the model.
output_path = "/content/drive/MyDrive/colab/piper" #@param {type:"string"}
output_dir = output_path+"/"+model_name
if not os.path.exists(output_dir):
  os.makedirs(output_dir)
#@markdown ---
#@markdown ### Choose dataset format:
dataset_format = "ljspeech" #@param ["ljspeech", "mycroft"]
#@markdown ---
#@markdown ### Is this a single speaker dataset? Otherwise, uncheck:
single_speaker = True #@param {type:"boolean"}
if single_speaker:
  force_sp = " --single-speaker"
else:
  force_sp = ""
#@markdown ---
#@markdown ### Select the sample rate of the dataset:
sample_rate = "22050" #@param ["16000", "22050"]
#@markdown ---
%cd /content/piper/src/python
#@markdown ### Do you want to train using this sample rate, but your audios don't have it?
#@markdown The resampler helps you do it quickly!
resample = False #@param {type:"boolean"}
if resample:
  !python resample.py --input_dir "/content/dataset/wavs" --output_dir "/content/dataset/wavs_resampled" --output_sr {sample_rate} --file_ext "wav"
  !mv /content/dataset/wavs_resampled/* /content/dataset/wavs
#@markdown ---

!python -m piper_train.preprocess \
  --language {final_language} \
  --input-dir /content/dataset \
  --output-dir "{output_dir}" \
  --dataset-format {dataset_format} \
  --sample-rate {sample_rate} \
  {force_sp}

#@markdown # <font color="pink"> **4. Settings.** 🧰
import json
import ipywidgets as widgets
from IPython.display import display
from google.colab import output
import os
#@markdown ### Select the action to train this dataset:

#@markdown * The option to continue a training is self-explanatory. If you've previously trained a model with free colab, your time is up and you're considering training it some more, this is ideal for you. You just have to set the same settings that you set when you first trained this model.
#@markdown * The option to convert a single-speaker model to a multi-speaker model is self-explanatory, and for this it is important that you have processed a dataset that contains text and audio from all possible speakers that you want to train in your model.
#@markdown * The finetune option is used to train a dataset using a pretrained model, that is, train on that data. This option is ideal if you want to train a very small dataset (more than five minutes recommended).
#@markdown * The train from scratch option builds features such as dictionary and speech form from scratch, and this may take longer to converge. For this, hours of audio (8 at least) are recommended, which have a large collection of phonemes.

action = "finetune" #@param ["Continue training", "convert single-speaker to multi-speaker model", "finetune", "train from scratch"]
#@markdown ---
if action == "Continue training":
    if os.path.exists(f"{output_dir}/lightning_logs/version_0/checkpoints/last.ckpt"):
        ft_command = f'--resume_from_checkpoint "{output_dir}/lightning_logs/version_0/checkpoints/last.ckpt" '
        print(f"Continuing {model_name}'s training at: {output_dir}/lightning_logs/version_0/checkpoints/last.ckpt")
    else:
        raise Exception("Training cannot be continued as there is no checkpoint to continue at.")
elif action == "finetune":
    if os.path.exists(f"{output_dir}/lightning_logs/version_0/checkpoints/last.ckpt"):
        raise Exception("Oh no! You have already trained this model before, you cannot choose this option since your progress will be lost, and then your previous time will not count. Please select the option to continue a training.")
    else:
        ft_command = '--resume_from_checkpoint "/content/pretrained.ckpt" '
elif action == "convert single-speaker to multi-speaker model":
    if not single_speaker:
        ft_command = '--resume_from_single_speaker_checkpoint "/content/pretrained.ckpt" '
    else:
        raise Exception("This dataset is not a multi-speaker dataset!")
else:
    ft_command = ""
if action== "convert single-speaker to multi-speaker model" or action == "finetune":
    try:
        with open('/content/piper/notebooks/pretrained_models.json') as f:
            pretrained_models = json.load(f)
        if final_language in pretrained_models:
            models = pretrained_models[final_language]
            model_options = [(model_name, model_name) for model_name, model_url in models.items()]
            model_dropdown = widgets.Dropdown(description = "Choose pretrained model", options=model_options)
            download_button = widgets.Button(description="Download")
            def download_model(btn):
                model_name = model_dropdown.value
                model_url = pretrained_models[final_language][model_name]
                print("Downloading pretrained model...")
                if model_url.startswith("1"):
                    !gdown -q "{model_url}" -O "/content/pretrained.ckpt"
                elif model_url.startswith("https://drive.google.com/file/d/"):
                    !gdown -q "{model_url}" -O "/content/pretrained.ckpt" --fuzzy
                else:
                    !wget -q "{model_url}" -O "/content/pretrained.ckpt"
                model_dropdown.close()
                download_button.close()
                output.clear()
                if os.path.exists("/content/pretrained.ckpt"):
                    print("Model downloaded!")
                else:
                    raise Exception("Couldn't download the pretrained model!")
            download_button.on_click(download_model)
            display(model_dropdown, download_button)
        else:
            raise Exception(f"There are no pretrained models available for the language {final_language}")
    except FileNotFoundError:
        raise Exception("The pretrained_models.json file was not found.")
else:
    print("Warning: this model will be trained from scratch. You need at least 8 hours of data for everything to work decent. Good luck!")
#@markdown ### Choose batch size based on this dataset:
batch_size = 12 #@param {type:"integer"}
#@markdown ---
validation_split = 0.01
#@markdown ### Choose the quality for this model:

#@markdown * x-low - 16Khz audio, 5-7M params
#@markdown * medium - 22.05Khz audio, 15-20 params
#@markdown * high - 22.05Khz audio, 28-32M params
quality = "medium" #@param ["high", "x-low", "medium"]
#@markdown ---
#@markdown ### For how many epochs to save training checkpoints?
#@markdown The larger your dataset, you should set this saving interval to a smaller value, as epochs can progress longer time.
checkpoint_epochs = 5 #@param {type:"integer"}
#@markdown ---
#@markdown ### Step interval to generate model samples:
log_every_n_steps = 1000 #@param {type:"integer"}
#@markdown ---
#@markdown ### Training epochs:
max_epochs = 10000 #@param {type:"integer"}
#@markdown ---

#@markdown # <font color="pink"> **5. Train.** 🏋️‍♂️
#@markdown Run this cell to train your final model! If possible, some audio samples will be saved during training in the output folder.

get_ipython().system(f'''
python -m piper_train \
--dataset-dir "{output_dir}" \
--accelerator 'gpu' \
--devices 1 \
--batch-size {batch_size} \
--validation-split {validation_split} \
--num-test-examples 2 \
--quality {quality} \
--checkpoint-epochs {checkpoint_epochs} \
--log_every_n_steps {log_every_n_steps} \
--max_epochs {max_epochs} \
{ft_command}\
--precision 32
''')
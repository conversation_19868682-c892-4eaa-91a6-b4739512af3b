#!/usr/bin/env python3
"""
Simple test script for the Piper TTS API.
"""

import requests
import time

def test_api():
    """Test the Piper TTS API."""
    base_url = "http://localhost:8000"
    
    # Test health check
    print("Testing health check...")
    try:
        response = requests.get(f"{base_url}/")
        print(f"Health check: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"Health check failed: {e}")
        return
    
    # Test voice info
    print("\nTesting voice info...")
    try:
        response = requests.get(f"{base_url}/voice/info")
        if response.status_code == 200:
            print(f"Voice info: {response.json()}")
        else:
            print(f"Voice info failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Voice info failed: {e}")
    
    # Test speakers
    print("\nTesting speakers...")
    try:
        response = requests.get(f"{base_url}/speakers")
        if response.status_code == 200:
            print(f"Speakers: {response.json()}")
        else:
            print(f"Speakers failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Speakers failed: {e}")
    
    # Test synthesis
    print("\nTesting synthesis...")
    try:
        test_text = "नमस्ते, यो एक परीक्षण हो।"  # "Hello, this is a test." in Nepali
        
        response = requests.get(
            f"{base_url}/synthesize",
            params={
                "text": test_text,
                "speaker_id": 0,
                "speech_rate": 1.0
            }
        )
        
        if response.status_code == 200:
            # Save the audio file
            with open("test_synthesis.wav", "wb") as f:
                f.write(response.content)
            print(f"Synthesis successful! Audio saved to test_synthesis.wav")
            print(f"Audio size: {len(response.content)} bytes")
        else:
            print(f"Synthesis failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Synthesis failed: {e}")

if __name__ == "__main__":
    print("Testing Piper TTS API...")
    print("Make sure the API server is running on http://localhost:8000")
    print("You can start it with: python run_piper_api.py")
    print()
    
    # Wait a moment for user to start server if needed
    input("Press Enter when the server is running...")
    
    test_api()
